-- Dummy Employee Data for <PERSON><PERSON>b
-- Indonesian Employee Health Management System
-- This script creates comprehensive dummy data including users, profiles, and employees

-- Start transaction
BEGIN;

-- Disable triggers temporarily for bulk insert
SET session_replication_role = replica;

-- Insert Users (Authentication data)
INSERT INTO users (id, email, password_hash, email_confirmed, created_at, updated_at) VALUES
-- Admin Users
('11111111-1111-1111-1111-111111111111', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('11111111-1111-1111-1111-111111111112', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('11111111-1111-1111-1111-111111111113', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),

-- Manager Users
('*************-2222-2222-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-2222-2222-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-2222-2222-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-2222-2222-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),

-- Doctor Users
('*************-3333-3333-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-3333-3333-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-3333-3333-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-3333-3333-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-3333-3333-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),

-- Nurse Users
('*************-4444-4444-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-4444-4444-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-4444-4444-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-4444-4444-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-4444-4444-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),

-- Pharmacist Users
('*************-5555-5555-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-5555-5555-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-5555-5555-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),

-- Technician Users
('*************-6666-6666-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-6666-6666-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-6666-6666-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-6666-6666-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),

-- Receptionist Users
('*************-7777-7777-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-7777-7777-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW()),
('*************-7777-7777-************', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, NOW(), NOW());

-- Insert Profiles (User profile data)
INSERT INTO profiles (id, first_name, last_name, email, role, created_at, updated_at) VALUES
-- Admin Profiles
('11111111-1111-1111-1111-111111111111', 'Sari', 'Wijayanti', '<EMAIL>', 'admin', NOW(), NOW()),
('11111111-1111-1111-1111-111111111112', 'Budi', 'Santoso', '<EMAIL>', 'admin', NOW(), NOW()),
('11111111-1111-1111-1111-111111111113', 'Dewi', 'Kusuma', '<EMAIL>', 'admin', NOW(), NOW()),

-- Manager Profiles
('*************-2222-2222-************', 'Agus', 'Prasetyo', '<EMAIL>', 'manager', NOW(), NOW()),
('*************-2222-2222-************', 'Rina', 'Sari', '<EMAIL>', 'manager', NOW(), NOW()),
('*************-2222-2222-************', 'Hendra', 'Gunawan', '<EMAIL>', 'manager', NOW(), NOW()),
('*************-2222-2222-************', 'Maya', 'Indrawati', '<EMAIL>', 'manager', NOW(), NOW()),

-- Doctor Profiles
('*************-3333-3333-************', 'Ahmad', 'Fauzi', '<EMAIL>', 'doctor', NOW(), NOW()),
('*************-3333-3333-************', 'Sinta', 'Maharani', '<EMAIL>', 'doctor', NOW(), NOW()),
('*************-3333-3333-************', 'Bambang', 'Sutrisno', '<EMAIL>', 'doctor', NOW(), NOW()),
('*************-3333-3333-************', 'Lestari', 'Handayani', '<EMAIL>', 'doctor', NOW(), NOW()),
('*************-3333-3333-************', 'Rudi', 'Hermawan', '<EMAIL>', 'doctor', NOW(), NOW()),

-- Nurse Profiles
('*************-4444-4444-************', 'Indah', 'Permatasari', '<EMAIL>', 'nurse', NOW(), NOW()),
('*************-4444-4444-************', 'Wati', 'Suryani', '<EMAIL>', 'nurse', NOW(), NOW()),
('*************-4444-4444-************', 'Joko', 'Widodo', '<EMAIL>', 'nurse', NOW(), NOW()),
('*************-4444-4444-************', 'Fitri', 'Rahayu', '<EMAIL>', 'nurse', NOW(), NOW()),
('*************-4444-4444-************', 'Andi', 'Setiawan', '<EMAIL>', 'nurse', NOW(), NOW()),

-- Pharmacist Profiles
('*************-5555-5555-************', 'Nurul', 'Hidayah', '<EMAIL>', 'pharmacist', NOW(), NOW()),
('*************-5555-5555-************', 'Dimas', 'Pratama', '<EMAIL>', 'pharmacist', NOW(), NOW()),
('*************-5555-5555-************', 'Ratna', 'Sari', '<EMAIL>', 'pharmacist', NOW(), NOW()),

-- Technician Profiles
('*************-6666-6666-************', 'Fajar', 'Nugroho', '<EMAIL>', 'technician', NOW(), NOW()),
('*************-6666-6666-************', 'Sari', 'Dewi', '<EMAIL>', 'technician', NOW(), NOW()),
('*************-6666-6666-************', 'Wahyu', 'Saputra', '<EMAIL>', 'technician', NOW(), NOW()),
('*************-6666-6666-************', 'Putri', 'Maharani', '<EMAIL>', 'technician', NOW(), NOW()),

-- Receptionist Profiles
('*************-7777-7777-************', 'Ani', 'Susanti', '<EMAIL>', 'receptionist', NOW(), NOW()),
('*************-7777-7777-************', 'Tono', 'Sugiarto', '<EMAIL>', 'receptionist', NOW(), NOW()),
('*************-7777-7777-************', 'Lina', 'Marlina', '<EMAIL>', 'receptionist', NOW(), NOW());

-- Insert Employee Records (Detailed employee information)
INSERT INTO employees (
    id, employee_id, first_name, last_name, email, phone, role, department, position,
    join_date, status, shift, salary, address, user_id, created_at, updated_at,
    emergency_contact, certifications, skills
) VALUES
-- Admin Employees
(gen_random_uuid(), 'EMP001', 'Sari', 'Wijayanti', '<EMAIL>', '+62812-3456-7890', 'admin', 'administration', 'System Administrator', '2023-01-15', 'active', 'regular', 15000000.00, 'Jl. Sudirman No. 123, Jakarta Pusat', '11111111-1111-1111-1111-111111111111', NOW(), NOW(), '{"name": "Budi Wijayanti", "relationship": "Suami", "phone": "+62812-3456-7891", "email": "<EMAIL>"}', '{"System Administration", "Database Management", "Security Management"}', '{"PostgreSQL", "Linux", "Network Security", "Project Management"}'),

(gen_random_uuid(), 'EMP002', 'Budi', 'Santoso', '<EMAIL>', '+62813-4567-8901', 'admin', 'administration', 'IT Administrator', '2023-02-01', 'active', 'regular', 14500000.00, 'Jl. Thamrin No. 456, Jakarta Pusat', '11111111-1111-1111-1111-111111111112', NOW(), NOW(), '{"name": "Siti Santoso", "relationship": "Istri", "phone": "+62813-4567-8902", "email": "<EMAIL>"}', '{"Network Administration", "Server Management", "Backup & Recovery"}', '{"Windows Server", "VMware", "Cisco", "Active Directory"}'),

(gen_random_uuid(), 'EMP003', 'Dewi', 'Kusuma', '<EMAIL>', '+62814-5678-9012', 'admin', 'administration', 'HR Administrator', '2023-03-10', 'active', 'regular', 13500000.00, 'Jl. Gatot Subroto No. 789, Jakarta Selatan', '11111111-1111-1111-1111-111111111113', NOW(), NOW(), '{"name": "Andi Kusuma", "relationship": "Suami", "phone": "+62814-5678-9013", "email": "<EMAIL>"}', '{"Human Resources", "Employee Relations", "Payroll Management"}', '{"HRIS", "Recruitment", "Training & Development", "Labor Law"}'),

-- Manager Employees
(gen_random_uuid(), 'EMP004', 'Agus', 'Prasetyo', '<EMAIL>', '+62815-6789-0123', 'manager', 'emergency', 'Emergency Department Manager', '2022-06-15', 'active', 'rotating', 18000000.00, 'Jl. Rasuna Said No. 321, Jakarta Selatan', '*************-2222-2222-************', NOW(), NOW(), '{"name": "Rina Prasetyo", "relationship": "Istri", "phone": "+62815-6789-0124", "email": "<EMAIL>"}', '{"Emergency Medicine", "Crisis Management", "Team Leadership"}', '{"Emergency Protocols", "Triage", "Staff Management", "Quality Assurance"}'),

(gen_random_uuid(), 'EMP005', 'Rina', 'Sari', '<EMAIL>', '+62816-7890-1234', 'manager', 'surgery', 'Surgery Department Manager', '2022-08-20', 'active', 'morning', 17500000.00, 'Jl. HR Rasuna Said No. 654, Jakarta Selatan', '*************-2222-2222-************', NOW(), NOW(), '{"name": "Dedi Sari", "relationship": "Suami", "phone": "+62816-7890-1235", "email": "<EMAIL>"}', '{"Surgical Management", "Operating Room Protocols", "Staff Coordination"}', '{"OR Management", "Surgical Instruments", "Sterilization", "Patient Safety"}'),

(gen_random_uuid(), 'EMP006', 'Hendra', 'Gunawan', '<EMAIL>', '+62817-8901-2345', 'manager', 'cardiology', 'Cardiology Department Manager', '2022-04-10', 'active', 'regular', 19000000.00, 'Jl. Kuningan No. 987, Jakarta Selatan', '*************-2222-2222-************', NOW(), NOW(), '{"name": "Maya Gunawan", "relationship": "Istri", "phone": "+62817-8901-2346", "email": "<EMAIL>"}', '{"Cardiology Management", "Cardiac Care Protocols", "Department Leadership"}', '{"ECG", "Cardiac Monitoring", "Heart Disease", "Patient Care Management"}'),

(gen_random_uuid(), 'EMP007', 'Maya', 'Indrawati', '<EMAIL>', '+62818-9012-3456', 'manager', 'pharmacy', 'Pharmacy Department Manager', '2022-09-05', 'active', 'regular', 16500000.00, 'Jl. Senayan No. 147, Jakarta Pusat', '*************-2222-2222-************', NOW(), NOW(), '{"name": "Rudi Indrawati", "relationship": "Suami", "phone": "+62818-9012-3457", "email": "<EMAIL>"}', '{"Pharmacy Management", "Drug Safety", "Inventory Control"}', '{"Pharmaceutical Care", "Drug Interactions", "Inventory Management", "Regulatory Compliance"}'),

-- Doctor Employees
(gen_random_uuid(), 'EMP008', 'Ahmad', 'Fauzi', '<EMAIL>', '+62819-0123-4567', 'doctor', 'emergency', 'Emergency Medicine Specialist', '2021-03-15', 'active', 'rotating', 25000000.00, 'Jl. Menteng No. 258, Jakarta Pusat', '*************-3333-3333-************', NOW(), NOW(), '{"name": "Fatimah Fauzi", "relationship": "Istri", "phone": "+62819-0123-4568", "email": "<EMAIL>"}', '{"Emergency Medicine", "ACLS", "ATLS", "Trauma Care"}', '{"Emergency Procedures", "Critical Care", "Resuscitation", "Trauma Management"}'),

(gen_random_uuid(), 'EMP009', 'Sinta', 'Maharani', '<EMAIL>', '+62820-1234-5678', 'doctor', 'pediatrics', 'Pediatrician', '2021-07-20', 'active', 'morning', 22000000.00, 'Jl. Kemang No. 369, Jakarta Selatan', '*************-3333-3333-************', NOW(), NOW(), '{"name": "Bambang Maharani", "relationship": "Suami", "phone": "+62820-1234-5679", "email": "<EMAIL>"}', '{"Pediatrics", "Child Development", "Vaccination", "PALS"}', '{"Child Care", "Immunization", "Growth Monitoring", "Pediatric Emergency"}'),

(gen_random_uuid(), 'EMP010', 'Bambang', 'Sutrisno', '<EMAIL>', '+62821-2345-6789', 'doctor', 'surgery', 'General Surgeon', '2020-11-10', 'active', 'morning', 28000000.00, 'Jl. Pondok Indah No. 741, Jakarta Selatan', '*************-3333-3333-************', NOW(), NOW(), '{"name": "Lestari Sutrisno", "relationship": "Istri", "phone": "+62821-2345-6790", "email": "<EMAIL>"}', '{"General Surgery", "Laparoscopic Surgery", "Trauma Surgery", "ATLS"}', '{"Surgical Procedures", "Minimally Invasive Surgery", "Post-operative Care", "Surgical Planning"}'),

(gen_random_uuid(), 'EMP011', 'Lestari', 'Handayani', '<EMAIL>', '+62822-3456-7890', 'doctor', 'cardiology', 'Cardiologist', '2020-05-25', 'active', 'regular', 30000000.00, 'Jl. Cipete No. 852, Jakarta Selatan', '*************-3333-3333-************', NOW(), NOW(), '{"name": "Rudi Handayani", "relationship": "Suami", "phone": "+62822-3456-7891", "email": "<EMAIL>"}', '{"Cardiology", "Interventional Cardiology", "Echocardiography", "ACLS"}', '{"Heart Disease", "Cardiac Catheterization", "ECG Interpretation", "Heart Failure Management"}'),

(gen_random_uuid(), 'EMP012', 'Rudi', 'Hermawan', '<EMAIL>', '+62823-4567-8901', 'doctor', 'orthopedics', 'Orthopedic Surgeon', '2021-01-08', 'active', 'morning', 26000000.00, 'Jl. Kebayoran No. 963, Jakarta Selatan', '*************-3333-3333-************', NOW(), NOW(), '{"name": "Indah Hermawan", "relationship": "Istri", "phone": "+62823-4567-8902", "email": "<EMAIL>"}', '{"Orthopedic Surgery", "Sports Medicine", "Joint Replacement", "Trauma Orthopedics"}', '{"Bone Surgery", "Joint Repair", "Fracture Treatment", "Arthroscopy"}'),

-- Nurse Employees
(gen_random_uuid(), 'EMP013', 'Indah', 'Permatasari', '<EMAIL>', '+62824-5678-9012', 'nurse', 'emergency', 'Emergency Nurse', '2022-02-14', 'active', 'rotating', 8500000.00, 'Jl. Tebet No. 159, Jakarta Selatan', '*************-4444-4444-************', NOW(), NOW(), '{"name": "Joko Permatasari", "relationship": "Suami", "phone": "+62824-5678-9013", "email": "<EMAIL>"}', '{"Emergency Nursing", "BLS", "ACLS", "Trauma Nursing"}', '{"Emergency Care", "IV Therapy", "Wound Care", "Patient Assessment"}'),

(gen_random_uuid(), 'EMP014', 'Wati', 'Suryani', '<EMAIL>', '+62825-6789-0123', 'nurse', 'surgery', 'Operating Room Nurse', '2022-04-18', 'active', 'morning', 9000000.00, 'Jl. Cikini No. 753, Jakarta Pusat', '*************-4444-4444-************', NOW(), NOW(), '{"name": "Andi Suryani", "relationship": "Suami", "phone": "+62825-6789-0124", "email": "<EMAIL>"}', '{"Operating Room Nursing", "Sterile Technique", "Surgical Instruments", "CNOR"}', '{"OR Procedures", "Sterilization", "Surgical Assistance", "Infection Control"}'),

(gen_random_uuid(), 'EMP015', 'Joko', 'Widodo', '<EMAIL>', '+62826-7890-1234', 'nurse', 'pediatrics', 'Pediatric Nurse', '2022-06-22', 'active', 'afternoon', 8200000.00, 'Jl. Matraman No. 486, Jakarta Timur', '*************-4444-4444-************', NOW(), NOW(), '{"name": "Fitri Widodo", "relationship": "Istri", "phone": "+62826-7890-1235", "email": "<EMAIL>"}', '{"Pediatric Nursing", "Child Development", "Family-Centered Care", "PALS"}', '{"Child Care", "Immunization", "Growth Assessment", "Family Education"}'),

(gen_random_uuid(), 'EMP016', 'Fitri', 'Rahayu', '<EMAIL>', '+62827-8901-2345', 'nurse', 'cardiology', 'Cardiac Nurse', '2022-08-30', 'active', 'morning', 8800000.00, 'Jl. Cempaka Putih No. 357, Jakarta Pusat', '*************-4444-4444-************', NOW(), NOW(), '{"name": "Budi Rahayu", "relationship": "Suami", "phone": "+62827-8901-2346", "email": "<EMAIL>"}', '{"Cardiac Nursing", "ECG Monitoring", "Cardiac Rehabilitation", "ACLS"}', '{"Heart Monitoring", "Cardiac Care", "Patient Education", "Medication Administration"}'),

(gen_random_uuid(), 'EMP017', 'Andi', 'Setiawan', '<EMAIL>', '+62828-9012-3456', 'nurse', 'orthopedics', 'Orthopedic Nurse', '2022-10-12', 'active', 'afternoon', 8300000.00, 'Jl. Senen No. 268, Jakarta Pusat', '*************-4444-4444-************', NOW(), NOW(), '{"name": "Sari Setiawan", "relationship": "Istri", "phone": "+62828-9012-3457", "email": "<EMAIL>"}', '{"Orthopedic Nursing", "Post-operative Care", "Mobility Assessment", "Pain Management"}', '{"Bone Care", "Physical Therapy", "Wound Care", "Patient Mobility"}'),

-- Pharmacist Employees
(gen_random_uuid(), 'EMP018', 'Nurul', 'Hidayah', '<EMAIL>', '+62829-0123-4567', 'pharmacist', 'pharmacy', 'Clinical Pharmacist', '2022-01-20', 'active', 'regular', 12000000.00, 'Jl. Tanah Abang No. 741, Jakarta Pusat', '*************-5555-5555-************', NOW(), NOW(), '{"name": "Dimas Hidayah", "relationship": "Suami", "phone": "+62829-0123-4568", "email": "<EMAIL>"}', '{"Clinical Pharmacy", "Drug Information", "Pharmaceutical Care", "Medication Therapy Management"}', '{"Drug Interactions", "Clinical Guidelines", "Patient Counseling", "Medication Review"}'),

(gen_random_uuid(), 'EMP019', 'Dimas', 'Pratama', '<EMAIL>', '+62830-1234-5678', 'pharmacist', 'pharmacy', 'Hospital Pharmacist', '2022-03-25', 'active', 'morning', 11500000.00, 'Jl. Salemba No. 852, Jakarta Pusat', '*************-5555-5555-************', NOW(), NOW(), '{"name": "Ratna Pratama", "relationship": "Istri", "phone": "+62830-1234-5679", "email": "<EMAIL>"}', '{"Hospital Pharmacy", "IV Admixture", "Drug Distribution", "Inventory Management"}', '{"Pharmaceutical Compounding", "Sterile Preparations", "Drug Storage", "Quality Control"}'),

(gen_random_uuid(), 'EMP020', 'Ratna', 'Sari', '<EMAIL>', '+62831-2345-6789', 'pharmacist', 'pharmacy', 'Emergency Pharmacist', '2022-05-15', 'active', 'rotating', 12500000.00, 'Jl. Kramat No. 963, Jakarta Pusat', '*************-5555-5555-************', NOW(), NOW(), '{"name": "Fajar Sari", "relationship": "Suami", "phone": "+62831-2345-6790", "email": "<EMAIL>"}', '{"Emergency Pharmacy", "Critical Care Pharmacy", "Drug Information", "Toxicology"}', '{"Emergency Medications", "Antidotes", "Critical Care", "Drug Calculations"}'),

-- Technician Employees
(gen_random_uuid(), 'EMP021', 'Fajar', 'Nugroho', '<EMAIL>', '+62832-3456-7890', 'technician', 'laboratory', 'Medical Laboratory Technician', '2022-07-08', 'active', 'morning', 7500000.00, 'Jl. Pramuka No. 147, Jakarta Timur', '*************-6666-6666-************', NOW(), NOW(), '{"name": "Sari Nugroho", "relationship": "Istri", "phone": "+62832-3456-7891", "email": "<EMAIL>"}', '{"Medical Laboratory Technology", "Clinical Chemistry", "Hematology", "Microbiology"}', '{"Lab Equipment", "Sample Analysis", "Quality Control", "Laboratory Safety"}'),

(gen_random_uuid(), 'EMP022', 'Sari', 'Dewi', '<EMAIL>', '+62833-4567-8901', 'technician', 'radiology', 'Radiology Technician', '2022-09-12', 'active', 'afternoon', 7800000.00, 'Jl. Rawamangun No. 258, Jakarta Timur', '*************-6666-6666-************', NOW(), NOW(), '{"name": "Wahyu Dewi", "relationship": "Suami", "phone": "+62833-4567-8902", "email": "<EMAIL>"}', '{"Radiology Technology", "X-ray", "CT Scan", "MRI", "Radiation Safety"}', '{"Medical Imaging", "Equipment Operation", "Patient Positioning", "Image Quality"}'),

(gen_random_uuid(), 'EMP023', 'Wahyu', 'Saputra', '<EMAIL>', '+62834-5678-9012', 'technician', 'maintenance', 'Biomedical Technician', '2022-11-20', 'active', 'regular', 8000000.00, 'Jl. Kelapa Gading No. 369, Jakarta Utara', '*************-6666-6666-************', NOW(), NOW(), '{"name": "Putri Saputra", "relationship": "Istri", "phone": "+62834-5678-9013", "email": "<EMAIL>"}', '{"Biomedical Engineering", "Equipment Maintenance", "Calibration", "Repair"}', '{"Medical Equipment", "Electronics", "Preventive Maintenance", "Troubleshooting"}'),

(gen_random_uuid(), 'EMP024', 'Putri', 'Maharani', '<EMAIL>', '+62835-6789-0123', 'technician', 'laboratory', 'Pathology Technician', '2023-01-25', 'active', 'morning', 7200000.00, 'Jl. Sunter No. 741, Jakarta Utara', '*************-6666-6666-************', NOW(), NOW(), '{"name": "Andi Maharani", "relationship": "Suami", "phone": "+62835-6789-0124", "email": "<EMAIL>"}', '{"Pathology", "Histology", "Cytology", "Specimen Processing"}', '{"Tissue Processing", "Microscopy", "Staining Techniques", "Laboratory Procedures"}'),

-- Receptionist Employees
(gen_random_uuid(), 'EMP025', 'Ani', 'Susanti', '<EMAIL>', '+62836-7890-1234', 'receptionist', 'administration', 'Front Desk Receptionist', '2023-02-10', 'active', 'morning', 5500000.00, 'Jl. Pluit No. 852, Jakarta Utara', '*************-7777-7777-************', NOW(), NOW(), '{"name": "Tono Susanti", "relationship": "Suami", "phone": "+62836-7890-1235", "email": "<EMAIL>"}', '{"Customer Service", "Medical Reception", "Appointment Scheduling", "Medical Records"}', '{"Patient Registration", "Phone Etiquette", "Computer Skills", "Medical Terminology"}'),

(gen_random_uuid(), 'EMP026', 'Tono', 'Sugiarto', '<EMAIL>', '+62837-8901-2345', 'receptionist', 'emergency', 'Emergency Reception', '2023-03-18', 'active', 'rotating', 6000000.00, 'Jl. Ancol No. 963, Jakarta Utara', '*************-7777-7777-************', NOW(), NOW(), '{"name": "Lina Sugiarto", "relationship": "Istri", "phone": "+62837-8901-2346", "email": "<EMAIL>"}', '{"Emergency Reception", "Crisis Communication", "Triage Support", "Medical Records"}', '{"Emergency Procedures", "Multi-tasking", "Stress Management", "Communication Skills"}'),

(gen_random_uuid(), 'EMP027', 'Lina', 'Marlina', '<EMAIL>', '+62838-9012-3456', 'receptionist', 'administration', 'Medical Records Clerk', '2023-04-22', 'active', 'afternoon', 5800000.00, 'Jl. Tanjung Priok No. 147, Jakarta Utara', '*************-7777-7777-************', NOW(), NOW(), '{"name": "Budi Marlina", "relationship": "Suami", "phone": "+62838-9012-3457", "email": "<EMAIL>"}', '{"Medical Records", "Data Entry", "Filing Systems", "HIPAA Compliance"}', '{"Medical Coding", "Database Management", "Document Management", "Attention to Detail"}');

-- Insert Departments (if not exists)
INSERT INTO departments (id, name, description, manager_id, is_active, created_at, updated_at)
SELECT * FROM (VALUES
    (gen_random_uuid(), 'Emergency Department', 'Emergency medical services and trauma care', (SELECT id FROM employees WHERE employee_id = 'EMP004'), true, NOW(), NOW()),
    (gen_random_uuid(), 'Surgery Department', 'Surgical procedures and operating room services', (SELECT id FROM employees WHERE employee_id = 'EMP005'), true, NOW(), NOW()),
    (gen_random_uuid(), 'Cardiology Department', 'Heart and cardiovascular care', (SELECT id FROM employees WHERE employee_id = 'EMP006'), true, NOW(), NOW()),
    (gen_random_uuid(), 'Pediatrics Department', 'Children and adolescent healthcare', NULL, true, NOW(), NOW()),
    (gen_random_uuid(), 'Orthopedics Department', 'Bone, joint, and musculoskeletal care', NULL, true, NOW(), NOW()),
    (gen_random_uuid(), 'Pharmacy Department', 'Medication management and pharmaceutical services', (SELECT id FROM employees WHERE employee_id = 'EMP007'), true, NOW(), NOW()),
    (gen_random_uuid(), 'Laboratory Department', 'Medical testing and diagnostic services', NULL, true, NOW(), NOW()),
    (gen_random_uuid(), 'Radiology Department', 'Medical imaging and diagnostic radiology', NULL, true, NOW(), NOW()),
    (gen_random_uuid(), 'Administration Department', 'Administrative and support services', NULL, true, NOW(), NOW()),
    (gen_random_uuid(), 'Maintenance Department', 'Facility and equipment maintenance', NULL, true, NOW(), NOW())
) AS new_departments(id, name, description, manager_id, is_active, created_at, updated_at)
WHERE NOT EXISTS (SELECT 1 FROM departments WHERE name = new_departments.name);

-- Re-enable triggers
SET session_replication_role = DEFAULT;

-- Commit transaction
COMMIT;

-- Summary Report
DO $$
DECLARE
    user_count INTEGER;
    profile_count INTEGER;
    employee_count INTEGER;
    department_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM users WHERE email LIKE '%@sehat-karyawan.com';
    SELECT COUNT(*) INTO profile_count FROM profiles WHERE email LIKE '%@sehat-karyawan.com';
    SELECT COUNT(*) INTO employee_count FROM employees WHERE email LIKE '%@sehat-karyawan.com';
    SELECT COUNT(*) INTO department_count FROM departments WHERE is_active = true;

    RAISE NOTICE '=== DUMMY DATA INSERTION SUMMARY ===';
    RAISE NOTICE 'Users created: %', user_count;
    RAISE NOTICE 'Profiles created: %', profile_count;
    RAISE NOTICE 'Employees created: %', employee_count;
    RAISE NOTICE 'Departments available: %', department_count;
    RAISE NOTICE '';
    RAISE NOTICE '=== ROLE DISTRIBUTION ===';
    RAISE NOTICE 'Admins: % employees', (SELECT COUNT(*) FROM employees WHERE role = 'admin');
    RAISE NOTICE 'Managers: % employees', (SELECT COUNT(*) FROM employees WHERE role = 'manager');
    RAISE NOTICE 'Doctors: % employees', (SELECT COUNT(*) FROM employees WHERE role = 'doctor');
    RAISE NOTICE 'Nurses: % employees', (SELECT COUNT(*) FROM employees WHERE role = 'nurse');
    RAISE NOTICE 'Pharmacists: % employees', (SELECT COUNT(*) FROM employees WHERE role = 'pharmacist');
    RAISE NOTICE 'Technicians: % employees', (SELECT COUNT(*) FROM employees WHERE role = 'technician');
    RAISE NOTICE 'Receptionists: % employees', (SELECT COUNT(*) FROM employees WHERE role = 'receptionist');
    RAISE NOTICE '';
    RAISE NOTICE '=== LOGIN CREDENTIALS ===';
    RAISE NOTICE 'All users have password: admin123';
    RAISE NOTICE 'Example logins:';
    RAISE NOTICE '  Admin: <EMAIL> / admin123';
    RAISE NOTICE '  Manager: <EMAIL> / admin123';
    RAISE NOTICE '  Doctor: <EMAIL> / admin123';
    RAISE NOTICE '  Nurse: <EMAIL> / admin123';
    RAISE NOTICE '';
    RAISE NOTICE '=== NEXT STEPS ===';
    RAISE NOTICE '1. Test login with any of the created accounts';
    RAISE NOTICE '2. Verify role-based access controls';
    RAISE NOTICE '3. Create additional schedules and leave requests as needed';
    RAISE NOTICE '4. Customize employee data as required';
    RAISE NOTICE '';
    RAISE NOTICE 'Dummy data insertion completed successfully!';
END $$;

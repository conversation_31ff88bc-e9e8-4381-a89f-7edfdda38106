# Dummy Employee Data for Se<PERSON> Karyawan Hub

This package contains comprehensive dummy employee data for the Indonesian Employee Health Management System, featuring authentic Indonesian names, realistic contact information, and proper role distribution.

## 📋 Contents

- **27 Employee Records** across all system roles
- **Complete User Authentication Data** (users, profiles, employees)
- **Indonesian Names and Addresses** with proper formatting
- **Role-based Distribution** for testing access controls
- **Multiple Import Formats** (SQL, JSON, CSV, API script)

## 👥 Role Distribution

| Role | Count | Description |
|------|-------|-------------|
| **Admin** | 3 | System administrators with full access |
| **Manager** | 4 | Department managers with management privileges |
| **Doctor** | 5 | Medical specialists across different departments |
| **Nurse** | 5 | Nursing staff in various specialties |
| **Pharmacist** | 3 | Pharmacy and medication management staff |
| **Technician** | 4 | Laboratory, radiology, and biomedical technicians |
| **Receptionist** | 3 | Front desk and administrative support |

## 🏥 Department Coverage

- **Emergency Department** - Emergency medicine and trauma care
- **Surgery Department** - Surgical procedures and OR services
- **Cardiology Department** - Heart and cardiovascular care
- **Pediatrics Department** - Children and adolescent healthcare
- **Orthopedics Department** - Bone, joint, and musculoskeletal care
- **Pharmacy Department** - Medication management and pharmaceutical services
- **Laboratory Department** - Medical testing and diagnostic services
- **Radiology Department** - Medical imaging and diagnostic radiology
- **Administration Department** - Administrative and support services
- **Maintenance Department** - Facility and equipment maintenance

## 🔐 Authentication Details

- **Default Password**: `admin123` for all accounts
- **Password Hash**: `$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S`
- **Email Confirmed**: All accounts are pre-verified
- **Username Format**: `<EMAIL>`

## 📁 File Descriptions

### 1. `dummy_employee_data.sql`
**Complete PostgreSQL import script**
- Creates users, profiles, employees, and departments
- Includes emergency contacts, certifications, and skills
- Transaction-safe with rollback capability
- Provides detailed summary report after import

### 2. `dummy_employee_data.json`
**Structured JSON format**
- Easy to parse for API imports
- Includes metadata and sample credentials
- Perfect for frontend testing and development

### 3. `dummy_employee_data.csv`
**Spreadsheet-compatible format**
- Can be imported into Excel, Google Sheets
- Useful for data analysis and reporting
- Semicolon-separated arrays for certifications/skills

### 4. `import_dummy_data.js`
**Automated API import script**
- Uses Express.js API endpoints
- Batch processing with error handling
- Configurable via command line arguments
- Includes authentication and retry logic

## 🚀 Import Methods

### Method 1: Direct SQL Import (Recommended)

```bash
# Import directly into PostgreSQL
psql -U postgres -d manajemen_karyawan -f dummy_employee_data.sql
```

### Method 2: API Import Script

```bash
# Install dependencies
npm install axios

# Run with default settings
node import_dummy_data.js

# Run with custom configuration
node import_dummy_data.js --api-url=http://localhost:3001 --admin-email=<EMAIL> --admin-password=admin123
```

### Method 3: Manual API Import

Use the JSON data with your API client:

```javascript
// Example using fetch
const employees = require('./dummy_employee_data.json').employees;

for (const employee of employees) {
  await fetch('/api/v1/employees', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(employee)
  });
}
```

### Method 4: CSV Import

1. Open `dummy_employee_data.csv` in Excel or Google Sheets
2. Use your preferred data import tool
3. Map columns to database fields
4. Import with proper data type conversion

## 🧪 Testing Credentials

### Sample Login Accounts

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| **Admin** | `<EMAIL>` | `admin123` | System Administrator |
| **Manager** | `<EMAIL>` | `admin123` | Emergency Dept Manager |
| **Doctor** | `<EMAIL>` | `admin123` | Emergency Medicine Specialist |
| **Nurse** | `<EMAIL>` | `admin123` | Emergency Nurse |
| **Pharmacist** | `<EMAIL>` | `admin123` | Clinical Pharmacist |
| **Technician** | `<EMAIL>` | `admin123` | Lab Technician |
| **Receptionist** | `<EMAIL>` | `admin123` | Front Desk |

## 📊 Data Features

### Personal Information
- **Authentic Indonesian Names** - First and last names following Indonesian naming conventions
- **Realistic Contact Info** - Indonesian phone number format (+62xxx-xxxx-xxxx)
- **Jakarta Addresses** - Real street names and districts in Jakarta
- **Emergency Contacts** - Family members with relationships and contact details

### Professional Information
- **Employee IDs** - Sequential format (EMP001-EMP027)
- **Role-appropriate Positions** - Realistic job titles for each role
- **Department Assignments** - Proper department-role mappings
- **Salary Ranges** - Indonesian Rupiah amounts appropriate for each role
- **Join Dates** - Realistic hiring dates over the past 2-3 years

### Healthcare-specific Data
- **Medical Certifications** - Role-appropriate professional certifications
- **Clinical Skills** - Relevant technical and clinical competencies
- **Shift Assignments** - Appropriate shift patterns for each role
- **Department Specializations** - Proper medical department assignments

## 🔧 Customization

### Modifying Employee Data

1. **Edit the SQL file** for database-level changes
2. **Update JSON file** for API-based modifications
3. **Modify CSV file** for spreadsheet-based editing
4. **Adjust import script** for custom processing logic

### Adding More Employees

Follow the existing patterns:
- Use sequential employee IDs (EMP028, EMP029, etc.)
- Maintain Indonesian naming conventions
- Assign appropriate roles and departments
- Include emergency contacts and professional data

### Changing Default Password

Update the password hash in all files:
```bash
# Generate new hash
node -e "console.log(require('bcryptjs').hashSync('newpassword', 12))"
```

## ⚠️ Important Notes

1. **Development Use Only** - This data is for testing and development
2. **Change Passwords** - Update default passwords before production use
3. **Data Privacy** - All personal information is fictional
4. **Database Backup** - Always backup your database before importing
5. **Role Permissions** - Verify role-based access controls after import

## 🆘 Troubleshooting

### Common Issues

**Authentication Failed**
- Ensure admin user exists in the system
- Check API URL and credentials
- Verify backend server is running

**Duplicate Key Errors**
- Employee IDs or emails already exist
- Clear existing test data or use different IDs

**Permission Denied**
- Admin user lacks required permissions
- Check role assignments and permissions

**API Connection Issues**
- Verify backend server is running
- Check API URL and port
- Ensure CORS is properly configured

### Getting Help

1. Check the application logs for detailed error messages
2. Verify database schema matches the expected structure
3. Test API endpoints manually before running import scripts
4. Ensure all required dependencies are installed

## 📝 License

This dummy data is provided for development and testing purposes. All names, addresses, and contact information are fictional and created specifically for this system.

// Authentication routes
import { Router, Response } from 'express';
import { AuthService } from '@/services/authService';
import { authenticateToken, verifyRefreshToken, generateToken, AuthRequest } from '@/middleware/auth';
import { validate, validateWithDatabase, authSchemas, profileSchemas, createProfileUpdateSchema, createAuthRegisterSchema } from '@/middleware/validation';

const router = Router();

// Register endpoint
router.post('/register', validateWithDatabase((roles) => createAuthRegisterSchema(roles)), async (req, res: Response) => {
  try {
    const { email, password, firstName, lastName, role } = req.body;

    const result = await AuthService.register(email, password, firstName, lastName, role);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    res.status(201).json({
      message: 'User registered successfully',
      user: {
        id: result.user!.id,
        email: result.user!.email,
        profile: result.user!.profile
      }
    });
  } catch (error) {
    console.error('Register endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Login endpoint
router.post('/login', validate(authSchemas.login), async (req, res: Response) => {
  try {
    const { email, password } = req.body;

    const result = await AuthService.login(email, password);

    if (result.error) {
      return res.status(401).json({ error: result.error });
    }

    const { auth } = result;

    // Set refresh token as httpOnly cookie
    res.cookie('refresh_token', auth!.refresh_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
    });

    res.json({
      message: 'Login successful',
      user: auth!.user,
      access_token: auth!.access_token,
      expires_in: auth!.expires_in
    });
  } catch (error) {
    console.error('Login endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Logout endpoint
router.post('/logout', authenticateToken, async (req: AuthRequest, res: Response) => {
  try {
    // Clear refresh token cookie
    res.clearCookie('refresh_token');
    
    res.json({ message: 'Logout successful' });
  } catch (error) {
    console.error('Logout endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Logout endpoint
router.post('/logout', async (req, res: Response) => {
  try {
    // Clear refresh token cookie
    res.clearCookie('refresh_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Refresh token endpoint
router.post('/refresh', async (req, res: Response) => {
  try {
    const refreshToken = req.cookies.refresh_token;

    if (!refreshToken) {
      return res.status(401).json({ error: 'Refresh token not provided' });
    }

    const decoded = verifyRefreshToken(refreshToken);
    if (!decoded) {
      return res.status(401).json({ error: 'Invalid refresh token' });
    }

    // Get user data
    const user = await AuthService.getUserById(decoded.userId);
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    // Generate new access token
    const newAccessToken = generateToken(user.id);
    const expiresIn = 7 * 24 * 60 * 60; // 7 days in seconds

    res.json({
      access_token: newAccessToken,
      expires_in: expiresIn,
      user: user
    });
  } catch (error) {
    console.error('Refresh token endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get current user endpoint
router.get('/me', authenticateToken, async (req: AuthRequest, res: Response) => {
  try {
    const user = await AuthService.getUserById(req.user!.id);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ user });
  } catch (error) {
    console.error('Get current user endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update profile endpoint
router.put('/profile', authenticateToken, validateWithDatabase((roles) => createProfileUpdateSchema(roles)), async (req: AuthRequest, res: Response) => {
  try {
    const { firstName, lastName, email, role } = req.body;
    
    const profileData: any = {};
    if (firstName !== undefined) profileData.first_name = firstName;
    if (lastName !== undefined) profileData.last_name = lastName;
    if (email !== undefined) profileData.email = email.toLowerCase();
    if (role !== undefined) profileData.role = role;

    const result = await AuthService.updateProfile(req.user!.id, profileData);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    res.json({
      message: 'Profile updated successfully',
      profile: result.profile
    });
  } catch (error) {
    console.error('Update profile endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Change password endpoint
router.put('/change-password', authenticateToken, validate(authSchemas.changePassword), async (req: AuthRequest, res: Response) => {
  try {
    const { currentPassword, newPassword } = req.body;

    const result = await AuthService.changePassword(req.user!.id, currentPassword, newPassword);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    res.json({ message: 'Password changed successfully' });
  } catch (error) {
    console.error('Change password endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Verify token endpoint (for frontend to check if token is still valid)
router.get('/verify', authenticateToken, async (req: AuthRequest, res: Response) => {
  try {
    res.json({
      valid: true,
      user: {
        id: req.user!.id,
        email: req.user!.email,
        role: req.user!.role
      }
    });
  } catch (error) {
    console.error('Verify token endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Forgot password endpoint
router.post('/forgot-password', validate(authSchemas.forgotPassword), async (req, res: Response) => {
  try {
    const { email } = req.body;

    const result = await AuthService.generatePasswordResetToken(email);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    // In production, you would send an email with the reset token
    // For development, we'll return the token (remove this in production)
    res.json({
      message: 'Password reset instructions sent to your email',
      ...(process.env.NODE_ENV === 'development' && { resetToken: result.token })
    });
  } catch (error) {
    console.error('Forgot password endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Reset password endpoint
router.post('/reset-password', validate(authSchemas.resetPassword), async (req, res: Response) => {
  try {
    const { token, newPassword } = req.body;

    const result = await AuthService.resetPassword(token, newPassword);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    res.json({ message: 'Password reset successfully' });
  } catch (error) {
    console.error('Reset password endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Verify email endpoint
router.post('/verify-email', async (req, res: Response) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ error: 'Verification token is required' });
    }

    const result = await AuthService.verifyEmail(token);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    res.json({ message: 'Email verified successfully' });
  } catch (error) {
    console.error('Verify email endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Resend email verification endpoint
router.post('/resend-verification', validate(authSchemas.forgotPassword), async (req, res: Response) => {
  try {
    const { email } = req.body;

    const result = await AuthService.resendEmailVerification(email);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    // In production, you would send an email with the verification token
    res.json({
      message: 'Verification email sent',
      ...(process.env.NODE_ENV === 'development' && { verificationToken: result.token })
    });
  } catch (error) {
    console.error('Resend verification endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;

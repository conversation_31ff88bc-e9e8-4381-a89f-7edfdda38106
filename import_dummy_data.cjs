#!/usr/bin/env node

/**
 * Import Dummy Employee Data Script
 * 
 * This script imports dummy employee data into the Sehat Karyawan Hub system
 * using the Express.js API endpoints.
 * 
 * Usage:
 *   node import_dummy_data.js [--api-url=http://localhost:3001] [--admin-email=<EMAIL>] [--admin-password=admin123]
 * 
 * Prerequisites:
 *   1. Backend server must be running
 *   2. Database must be set up with proper schema
 *   3. Admin user must exist for authentication
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiUrl: process.env.API_URL || 'http://localhost:3001/api/v1',
  adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>',
  adminPassword: process.env.ADMIN_PASSWORD || 'password123',
  batchSize: 5, // Number of employees to create in each batch
  delayBetweenBatches: 1000 // Delay in milliseconds between batches
};

// Parse command line arguments
process.argv.forEach(arg => {
  if (arg.startsWith('--api-url=')) {
    config.apiUrl = arg.split('=')[1];
  } else if (arg.startsWith('--admin-email=')) {
    config.adminEmail = arg.split('=')[1];
  } else if (arg.startsWith('--admin-password=')) {
    config.adminPassword = arg.split('=')[1];
  }
});

// Employee data
const employeeData = [
  {
    employeeId: 'EMP001',
    firstName: 'Sari',
    lastName: 'Wijayanti',
    email: '<EMAIL>',
    phone: '+62812-3456-7890',
    role: 'admin',
    department: 'administration',
    position: 'System Administrator',
    joinDate: '2023-01-15',
    status: 'active',
    shift: 'regular',
    salary: 15000000,
    address: 'Jl. Sudirman No. 123, Jakarta Pusat'
  },
  {
    employeeId: 'EMP002',
    firstName: 'Budi',
    lastName: 'Santoso',
    email: '<EMAIL>',
    phone: '+62813-4567-8901',
    role: 'admin',
    department: 'administration',
    position: 'IT Administrator',
    joinDate: '2023-02-01',
    status: 'active',
    shift: 'regular',
    salary: 14500000,
    address: 'Jl. Thamrin No. 456, Jakarta Pusat'
  },
  {
    employeeId: 'EMP004',
    firstName: 'Agus',
    lastName: 'Prasetyo',
    email: '<EMAIL>',
    phone: '+62815-6789-0123',
    role: 'manager',
    department: 'emergency',
    position: 'Emergency Department Manager',
    joinDate: '2022-06-15',
    status: 'active',
    shift: 'rotating',
    salary: 18000000,
    address: 'Jl. Rasuna Said No. 321, Jakarta Selatan'
  },
  {
    employeeId: 'EMP008',
    firstName: 'Ahmad',
    lastName: 'Fauzi',
    email: '<EMAIL>',
    phone: '+62819-0123-4567',
    role: 'doctor',
    department: 'emergency',
    position: 'Emergency Medicine Specialist',
    joinDate: '2021-03-15',
    status: 'active',
    shift: 'rotating',
    salary: 25000000,
    address: 'Jl. Menteng No. 258, Jakarta Pusat'
  },
  {
    employeeId: 'EMP013',
    firstName: 'Indah',
    lastName: 'Permatasari',
    email: '<EMAIL>',
    phone: '+62824-5678-9012',
    role: 'nurse',
    department: 'emergency',
    position: 'Emergency Nurse',
    joinDate: '2022-02-14',
    status: 'active',
    shift: 'rotating',
    salary: 8500000,
    address: 'Jl. Tebet No. 159, Jakarta Selatan'
  }
];

// Utility functions
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const logWithTimestamp = (message) => {
  console.log(`[${new Date().toISOString()}] ${message}`);
};

// API client
class ApiClient {
  constructor(baseURL) {
    this.client = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    this.token = null;
  }

  async authenticate(email, password) {
    try {
      logWithTimestamp('Authenticating admin user...');
      const response = await this.client.post('/auth/login', {
        email,
        password
      });

      this.token = response.data.access_token;
      this.client.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
      
      logWithTimestamp(`✅ Authentication successful for ${email}`);
      return true;
    } catch (error) {
      logWithTimestamp(`❌ Authentication failed: ${error.response?.data?.error || error.message}`);
      return false;
    }
  }

  async createUser(userData) {
    try {
      const response = await this.client.post('/admin/users', {
        email: userData.email,
        password: 'admin123', // Default password for all dummy users
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role,
        emailConfirmed: true
      });

      return { success: true, user: response.data.user };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || error.message 
      };
    }
  }

  async createEmployee(employeeData) {
    try {
      const response = await this.client.post('/employees', employeeData);
      return { success: true, employee: response.data.employee };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || error.message 
      };
    }
  }
}

// Main import function
async function importDummyData() {
  logWithTimestamp('🚀 Starting dummy data import...');
  logWithTimestamp(`API URL: ${config.apiUrl}`);
  logWithTimestamp(`Admin Email: ${config.adminEmail}`);
  
  const apiClient = new ApiClient(config.apiUrl);
  
  // Authenticate
  const authSuccess = await apiClient.authenticate(config.adminEmail, config.adminPassword);
  if (!authSuccess) {
    logWithTimestamp('❌ Import failed: Could not authenticate');
    process.exit(1);
  }

  let successCount = 0;
  let errorCount = 0;
  const errors = [];

  // Process employees in batches
  for (let i = 0; i < employeeData.length; i += config.batchSize) {
    const batch = employeeData.slice(i, i + config.batchSize);
    logWithTimestamp(`Processing batch ${Math.floor(i / config.batchSize) + 1}/${Math.ceil(employeeData.length / config.batchSize)}...`);

    for (const employee of batch) {
      try {
        // First create user account
        logWithTimestamp(`Creating user account for ${employee.firstName} ${employee.lastName}...`);
        const userResult = await apiClient.createUser(employee);
        
        if (!userResult.success) {
          if (userResult.error.includes('already exists')) {
            logWithTimestamp(`⚠️  User ${employee.email} already exists, skipping user creation`);
          } else {
            logWithTimestamp(`❌ Failed to create user ${employee.email}: ${userResult.error}`);
            errors.push(`User ${employee.email}: ${userResult.error}`);
            errorCount++;
            continue;
          }
        }

        // Then create employee record
        logWithTimestamp(`Creating employee record for ${employee.employeeId}...`);
        const employeeResult = await apiClient.createEmployee(employee);
        
        if (employeeResult.success) {
          logWithTimestamp(`✅ Successfully created employee ${employee.employeeId} - ${employee.firstName} ${employee.lastName}`);
          successCount++;
        } else {
          logWithTimestamp(`❌ Failed to create employee ${employee.employeeId}: ${employeeResult.error}`);
          errors.push(`Employee ${employee.employeeId}: ${employeeResult.error}`);
          errorCount++;
        }

      } catch (error) {
        logWithTimestamp(`❌ Unexpected error processing ${employee.employeeId}: ${error.message}`);
        errors.push(`Employee ${employee.employeeId}: ${error.message}`);
        errorCount++;
      }
    }

    // Delay between batches to avoid overwhelming the server
    if (i + config.batchSize < employeeData.length) {
      logWithTimestamp(`Waiting ${config.delayBetweenBatches}ms before next batch...`);
      await delay(config.delayBetweenBatches);
    }
  }

  // Summary
  logWithTimestamp('\n📊 Import Summary:');
  logWithTimestamp(`✅ Successfully created: ${successCount} employees`);
  logWithTimestamp(`❌ Failed: ${errorCount} employees`);
  
  if (errors.length > 0) {
    logWithTimestamp('\n❌ Errors encountered:');
    errors.forEach(error => logWithTimestamp(`  - ${error}`));
  }

  if (successCount > 0) {
    logWithTimestamp('\n🎉 Import completed! You can now login with any of the created accounts using password: admin123');
    logWithTimestamp('\nSample login credentials:');
    logWithTimestamp('  Admin: <EMAIL> / admin123');
    logWithTimestamp('  Manager: <EMAIL> / admin123');
    logWithTimestamp('  Doctor: <EMAIL> / admin123');
    logWithTimestamp('  Nurse: <EMAIL> / admin123');
  }
}

// Run the import
if (require.main === module) {
  importDummyData().catch(error => {
    logWithTimestamp(`💥 Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { importDummyData, ApiClient };

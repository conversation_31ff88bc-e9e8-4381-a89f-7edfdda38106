// Employee routes
import { Router, Response } from 'express';
import { EmployeeService } from '@/services/employeeService';
import { authenticateToken, requireManagerOrAdmin, AuthRequest } from '@/middleware/auth';
import { validateWithDatabase, createEmployeeCreateSchema, createEmployeeUpdateSchema } from '@/middleware/validation';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all employees (Manager/Admin only)
router.get('/', requireManagerOrAdmin, async (req: AuthRequest, res: Response) => {
  try {
    const { department, role, search } = req.query;

    let employees;

    if (search) {
      employees = await EmployeeService.search(search as string);
    } else if (department) {
      employees = await EmployeeService.getByDepartment(department as string);
    } else if (role) {
      employees = await EmployeeService.getByRole(role as string);
    } else {
      employees = await EmployeeService.getAll();
    }

    res.json({
      employees,
      total: employees.length
    });
  } catch (error) {
    console.error('Get employees endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch employees' });
  }
});

// Get employee statistics (Manager/Admin only)
router.get('/statistics', requireManagerOrAdmin, async (req: AuthRequest, res: Response) => {
  try {
    const statistics = await EmployeeService.getStatistics();
    res.json(statistics);
  } catch (error) {
    console.error('Get employee statistics endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch employee statistics' });
  }
});

// Get employee by ID
router.get('/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    // Users can view their own employee record, managers/admins can view any
    const employee = await EmployeeService.getById(id);
    
    if (!employee) {
      return res.status(404).json({ error: 'Employee not found' });
    }

    // Check if user can access this employee record
    const canAccess = 
      req.user!.role === 'admin' || 
      req.user!.role === 'manager' || 
      employee.user_id === req.user!.id;

    if (!canAccess) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({ employee });
  } catch (error) {
    console.error('Get employee by ID endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch employee' });
  }
});

// Create employee (Manager/Admin only)
router.post('/', requireManagerOrAdmin, validateWithDatabase(createEmployeeCreateSchema), async (req: AuthRequest, res: Response) => {
  try {
    const employeeData = {
      employee_id: req.body.employeeId,
      first_name: req.body.firstName,
      last_name: req.body.lastName,
      email: req.body.email,
      phone: req.body.phone,
      role: req.body.role,
      department: req.body.department,
      position: req.body.position,
      join_date: new Date(req.body.joinDate),
      status: req.body.status || 'active',
      shift: req.body.shift,
      salary: req.body.salary,
      avatar: req.body.avatar,
      address: req.body.address,
      user_id: req.body.userId,
    };

    const employee = await EmployeeService.create(employeeData, req.user!.id);

    res.status(201).json({
      message: 'Employee created successfully',
      employee
    });
  } catch (error) {
    console.error('Create employee endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('already exists')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to create employee' });
  }
});

// Update employee
router.put('/:id', validateWithDatabase(createEmployeeUpdateSchema), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    // Check if user can update this employee record
    const existingEmployee = await EmployeeService.getById(id);
    if (!existingEmployee) {
      return res.status(404).json({ error: 'Employee not found' });
    }

    const canUpdate = 
      req.user!.role === 'admin' || 
      req.user!.role === 'manager' || 
      (existingEmployee.user_id === req.user!.id && req.user!.role !== 'nurse'); // Users can update their own record (except nurses for security)

    if (!canUpdate) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Convert camelCase to snake_case for database
    const updateData: any = {};
    if (req.body.employeeId !== undefined) updateData.employee_id = req.body.employeeId;
    if (req.body.firstName !== undefined) updateData.first_name = req.body.firstName;
    if (req.body.lastName !== undefined) updateData.last_name = req.body.lastName;
    if (req.body.email !== undefined) updateData.email = req.body.email;
    if (req.body.phone !== undefined) updateData.phone = req.body.phone;
    if (req.body.role !== undefined) updateData.role = req.body.role;
    if (req.body.department !== undefined) updateData.department = req.body.department;
    if (req.body.position !== undefined) updateData.position = req.body.position;
    if (req.body.joinDate !== undefined) updateData.join_date = new Date(req.body.joinDate);
    if (req.body.status !== undefined) updateData.status = req.body.status;
    if (req.body.shift !== undefined) updateData.shift = req.body.shift;
    if (req.body.salary !== undefined) updateData.salary = req.body.salary;
    if (req.body.avatar !== undefined) updateData.avatar = req.body.avatar;
    if (req.body.address !== undefined) updateData.address = req.body.address;

    // Restrict certain fields for non-admin users
    if (req.user!.role !== 'admin') {
      delete updateData.role;
      delete updateData.salary;
      delete updateData.status;
    }

    const employee = await EmployeeService.update(id, updateData);

    res.json({
      message: 'Employee updated successfully',
      employee
    });
  } catch (error) {
    console.error('Update employee endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('already exists')) {
        return res.status(409).json({ error: error.message });
      }
      if (error.message === 'Employee not found') {
        return res.status(404).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to update employee' });
  }
});

// Delete employee (Admin only)
router.delete('/:id', requireManagerOrAdmin, async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    await EmployeeService.delete(id);

    res.json({ message: 'Employee deleted successfully' });
  } catch (error) {
    console.error('Delete employee endpoint error:', error);
    
    if (error instanceof Error && error.message === 'Employee not found') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to delete employee' });
  }
});

export default router;

// Test script to verify dynamic validation works correctly
const axios = require('axios');

const API_BASE = 'http://localhost:3001/api/v1';

async function testDynamicValidation() {
  console.log('🧪 Testing Dynamic Validation...\n');

  try {
    // Test 1: Get available roles and departments
    console.log('1. Fetching available roles and departments...');
    const rolesResponse = await axios.get(`${API_BASE}/management/roles`, {
      headers: {
        'Authorization': 'Bearer your-test-token-here' // Replace with actual token
      }
    });
    
    const departmentsResponse = await axios.get(`${API_BASE}/management/departments`, {
      headers: {
        'Authorization': 'Bearer your-test-token-here' // Replace with actual token
      }
    });

    const roles = rolesResponse.data.roles.map(r => r.name);
    const departments = departmentsResponse.data.departments.map(d => d.name.toLowerCase());
    
    console.log('✅ Available roles:', roles);
    console.log('✅ Available departments:', departments);

    // Test 2: Try to create employee with valid role/department
    console.log('\n2. Testing employee creation with valid role/department...');
    const validEmployeeData = {
      employeeId: 'TEST001',
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      role: roles[0], // Use first available role
      department: departments[0], // Use first available department
      position: 'Test Position',
      joinDate: new Date().toISOString().split('T')[0],
      shift: 'morning'
    };

    try {
      const createResponse = await axios.post(`${API_BASE}/employees`, validEmployeeData, {
        headers: {
          'Authorization': 'Bearer your-test-token-here' // Replace with actual token
        }
      });
      console.log('✅ Employee creation with valid data succeeded');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('⚠️  Authentication required - please update the token in this script');
      } else {
        console.log('✅ Employee creation validation passed (other error occurred)');
      }
    }

    // Test 3: Try to create employee with invalid role
    console.log('\n3. Testing employee creation with invalid role...');
    const invalidRoleData = {
      ...validEmployeeData,
      employeeId: 'TEST002',
      email: '<EMAIL>',
      role: 'invalid_role'
    };

    try {
      await axios.post(`${API_BASE}/employees`, invalidRoleData, {
        headers: {
          'Authorization': 'Bearer your-test-token-here' // Replace with actual token
        }
      });
      console.log('❌ Employee creation with invalid role should have failed');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Employee creation with invalid role correctly rejected');
        console.log('   Error:', error.response.data.details);
      } else if (error.response?.status === 401) {
        console.log('⚠️  Authentication required - cannot test validation');
      } else {
        console.log('❌ Unexpected error:', error.response?.data || error.message);
      }
    }

    // Test 4: Try to create employee with invalid department
    console.log('\n4. Testing employee creation with invalid department...');
    const invalidDeptData = {
      ...validEmployeeData,
      employeeId: 'TEST003',
      email: '<EMAIL>',
      department: 'invalid_department'
    };

    try {
      await axios.post(`${API_BASE}/employees`, invalidDeptData, {
        headers: {
          'Authorization': 'Bearer your-test-token-here' // Replace with actual token
        }
      });
      console.log('❌ Employee creation with invalid department should have failed');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Employee creation with invalid department correctly rejected');
        console.log('   Error:', error.response.data.details);
      } else if (error.response?.status === 401) {
        console.log('⚠️  Authentication required - cannot test validation');
      } else {
        console.log('❌ Unexpected error:', error.response?.data || error.message);
      }
    }

    console.log('\n🎉 Dynamic validation tests completed!');
    console.log('\nNote: To run full tests, please:');
    console.log('1. Replace "your-test-token-here" with a valid admin/manager token');
    console.log('2. Ensure the backend is running on port 3001');
    console.log('3. Ensure you have roles and departments in the database');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

// Run the test
testDynamicValidation();

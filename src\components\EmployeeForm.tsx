
import React, { useState, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, User, MapPin, Calendar, DollarSign, Clock } from 'lucide-react';
import { Employee, EmployeeStatus, Shift } from '@/types/employee';
import { useManagement } from '@/hooks/useManagementNew';

// Create a dynamic schema factory that uses fetched data for validation
const createEmployeeSchema = (roles: Array<{name: string}>, departments: Array<{name: string}>) => {
  const roleNames = roles.map(role => role.name);
  const departmentNames = departments.map(dept => dept.name.toLowerCase());

  return z.object({
    employeeId: z.string().min(1, 'Employee ID wajib diisi'),
    firstName: z.string().min(1, 'Nama depan wajib diisi'),
    lastName: z.string().min(1, 'Nama belakang wajib diisi'),
    email: z.string().email('Format email tidak valid'),
    phone: z.string().optional(),
    role: z.string().refine(
      (value) => roleNames.includes(value),
      { message: 'Role tidak valid' }
    ),
    department: z.string().refine(
      (value) => departmentNames.includes(value),
      { message: 'Departemen tidak valid' }
    ),
    position: z.string().min(1, 'Posisi wajib diisi'),
    joinDate: z.string().min(1, 'Tanggal bergabung wajib diisi'),
    status: z.enum(['active', 'inactive', 'on_leave', 'terminated']),
    shift: z.enum(['morning', 'afternoon', 'night', 'rotating', 'regular']),
    salary: z.number().min(0, 'Gaji tidak boleh negatif').optional(),
    address: z.string().optional(),
  });
};

interface EmployeeFormProps {
  employee?: Employee;
  onSubmit: (data: z.infer<typeof employeeSchema>) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export default function EmployeeForm({ employee, onSubmit, onCancel, isLoading }: EmployeeFormProps) {
  const {
    roles,
    departments,
    positions,
    isLoadingRoles,
    isLoadingDepartments,
    isLoadingPositions
  } = useManagement();

  // Create dynamic schema based on fetched data
  const employeeSchema = useMemo(() => {
    if (roles.length === 0 || departments.length === 0) {
      // Return a basic schema while data is loading
      return z.object({
        employeeId: z.string().min(1, 'Employee ID wajib diisi'),
        firstName: z.string().min(1, 'Nama depan wajib diisi'),
        lastName: z.string().min(1, 'Nama belakang wajib diisi'),
        email: z.string().email('Format email tidak valid'),
        phone: z.string().optional(),
        role: z.string().min(1, 'Role wajib diisi'),
        department: z.string().min(1, 'Departemen wajib diisi'),
        position: z.string().min(1, 'Posisi wajib diisi'),
        joinDate: z.string().min(1, 'Tanggal bergabung wajib diisi'),
        status: z.enum(['active', 'inactive', 'on_leave', 'terminated']),
        shift: z.enum(['morning', 'afternoon', 'night', 'rotating', 'regular']),
        salary: z.number().min(0, 'Gaji tidak boleh negatif').optional(),
        address: z.string().optional(),
      });
    }
    return createEmployeeSchema(roles, departments);
  }, [roles, departments]);

  const { register, handleSubmit, setValue, watch, formState: { errors } } = useForm<z.infer<typeof employeeSchema>>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      employeeId: employee?.employeeId || '',
      firstName: employee?.firstName || '',
      lastName: employee?.lastName || '',
      email: employee?.email || '',
      phone: employee?.phone || '',
      role: employee?.role || (roles.length > 0 ? roles[0].name : ''),
      department: employee?.department || (departments.length > 0 ? departments[0].name.toLowerCase() : ''),
      position: employee?.position || '',
      joinDate: employee?.joinDate || new Date().toISOString().split('T')[0],
      status: employee?.status || 'active',
      shift: employee?.shift || 'morning',
      salary: employee?.salary || undefined,
      address: employee?.address || '',
    },
  });

  const [selectedRole, setSelectedRole] = useState<string>(employee?.role || '');
  const [selectedDepartment, setSelectedDepartment] = useState<string>(employee?.department || '');
  const [selectedStatus, setSelectedStatus] = useState<EmployeeStatus>(employee?.status || 'active');
  const [selectedShift, setSelectedShift] = useState<Shift>(employee?.shift || 'morning');

  const watchedDepartment = watch('department');

  // Set initial values when data loads
  useEffect(() => {
    if (roles.length > 0 && !selectedRole && !employee) {
      const defaultRole = roles[0].name;
      setSelectedRole(defaultRole);
      setValue('role', defaultRole);
    }
    if (departments.length > 0 && !selectedDepartment && !employee) {
      const defaultDepartment = departments[0].name.toLowerCase();
      setSelectedDepartment(defaultDepartment);
      setValue('department', defaultDepartment);
    }
  }, [roles, departments, selectedRole, selectedDepartment, employee, setValue]);

  // Filter positions based on selected department
  const filteredPositions = positions.filter(position =>
    position.department_id && departments.find(dept =>
      dept.name.toLowerCase() === watchedDepartment.toLowerCase() && dept.id === position.department_id
    )
  );

  console.log(watchedDepartment);
  console.log(departments);
  console.log(positions);
  console.log(filteredPositions);

  // const filteredPositions = positions.filter(position => position.departmentId.toLowerCase() === watchedDepartment.toLowerCase());

  // Loading state
  const isFormLoading = isLoadingRoles || isLoadingDepartments || isLoadingPositions;

  if (isFormLoading) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-muted-foreground">Memuat data form...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const roleOptions = roles.map(role => ({
    value: role.name,
    label: role.name.charAt(0).toUpperCase() + role.name.slice(1),
    description: role.description
  }));

  const departmentOptions = departments.map(dept => ({
    value: dept.name.toLowerCase(),
    label: dept.name.charAt(0).toUpperCase() + dept.name.slice(1),
    description: dept.description
  }));

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader className="pb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <User className="h-6 w-6 text-primary" />
          </div>
          <div>
            <CardTitle className="text-2xl">
              {employee ? 'Edit Karyawan' : 'Tambah Karyawan Baru'}
            </CardTitle>
            <p className="text-muted-foreground mt-1">
              {employee ? 'Perbarui informasi karyawan' : 'Lengkapi formulir untuk menambah karyawan baru'}
            </p>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          {/* Personal Information Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 pb-2 border-b">
              <User className="h-4 w-4 text-primary" />
              <h3 className="font-semibold text-lg">Informasi Personal</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="employeeId" className="text-sm font-medium">
                  ID Karyawan <span className="text-red-500">*</span>
                </Label>
                <Input 
                  id="employeeId" 
                  placeholder="Contoh: EMP001"
                  {...register('employeeId')} 
                  className={errors.employeeId ? 'border-red-500' : ''}
                />
                {errors.employeeId && (
                  <p className="text-red-500 text-xs mt-1">{errors.employeeId.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-sm font-medium">
                  Nama Depan <span className="text-red-500">*</span>
                </Label>
                <Input 
                  id="firstName" 
                  placeholder="Nama depan"
                  {...register('firstName')} 
                  className={errors.firstName ? 'border-red-500' : ''}
                />
                {errors.firstName && (
                  <p className="text-red-500 text-xs mt-1">{errors.firstName.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-sm font-medium">
                  Nama Belakang <span className="text-red-500">*</span>
                </Label>
                <Input 
                  id="lastName" 
                  placeholder="Nama belakang"
                  {...register('lastName')} 
                  className={errors.lastName ? 'border-red-500' : ''}
                />
                {errors.lastName && (
                  <p className="text-red-500 text-xs mt-1">{errors.lastName.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium">
                  Email <span className="text-red-500">*</span>
                </Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="<EMAIL>"
                  {...register('email')} 
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone" className="text-sm font-medium">
                  Nomor Telepon
                </Label>
                <Input 
                  id="phone" 
                  type="tel" 
                  placeholder="+62 812-3456-7890"
                  {...register('phone')} 
                />
              </div>
            </div>
          </div>

          {/* Job Information Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 pb-2 border-b">
              <MapPin className="h-4 w-4 text-primary" />
              <h3 className="font-semibold text-lg">Informasi Pekerjaan</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="role" className="text-sm font-medium">
                  Role <span className="text-red-500">*</span>
                </Label>
                <Select value={selectedRole} onValueChange={(value: string) => {
                  setSelectedRole(value);
                  setValue('role', value);
                }}>
                  <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Pilih role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roleOptions.map((role) => (
                      <SelectItem key={role.value} value={role.value}>
                        <div className="flex flex-col">
                          <span className="font-medium">{role.label}</span>
                          {role.description && (
                            <span className="text-xs text-muted-foreground">{role.description}</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.role && (
                  <p className="text-red-500 text-xs mt-1">{errors.role.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="department" className="text-sm font-medium">
                  Departemen <span className="text-red-500">*</span>
                </Label>
                <Select value={selectedDepartment} onValueChange={(value: string) => {
                  setSelectedDepartment(value);
                  setValue('department', value);
                  setValue('position', ''); // Reset position when department changes
                }}>
                  <SelectTrigger className={errors.department ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Pilih departemen" />
                  </SelectTrigger>
                  <SelectContent>
                    {departmentOptions.map((dept) => (
                      <SelectItem key={dept.value} value={dept.value}>
                        <div className="flex flex-col">
                          <span className="font-medium">{dept.label}</span>
                          {dept.description && (
                            <span className="text-xs text-muted-foreground">{dept.description}</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.department && (
                  <p className="text-red-500 text-xs mt-1">{errors.department.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="position" className="text-sm font-medium">
                  Posisi <span className="text-red-500">*</span>
                </Label>
                <Select onValueChange={(value) => setValue('position', value)}>
                  <SelectTrigger className={errors.position ? 'border-red-500' : ''}>
                    <SelectValue placeholder={filteredPositions.length > 0 ? "Pilih posisi" : "Pilih departemen dulu"} />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredPositions.length > 0 ? (
                      filteredPositions.map((position) => (
                        <SelectItem key={position.id} value={position.name}>
                          <div className="flex flex-col">
                            <span className="font-medium">{position.name}</span>
                            {position.description && (
                              <span className="text-xs text-muted-foreground">{position.description}</span>
                            )}
                          </div>
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="no-positions" disabled>
                        Tidak ada posisi tersedia
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {errors.position && (
                  <p className="text-red-500 text-xs mt-1">{errors.position.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="status" className="text-sm font-medium">
                  Status <span className="text-red-500">*</span>
                </Label>
                <Select value={selectedStatus} onValueChange={(value: EmployeeStatus) => {
                  setSelectedStatus(value);
                  setValue('status', value);
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">
                      <div className="flex items-center space-x-2">
                        <Badge variant="default" className="text-xs">Aktif</Badge>
                      </div>
                    </SelectItem>
                    <SelectItem value="inactive">
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="text-xs">Tidak Aktif</Badge>
                      </div>
                    </SelectItem>
                    <SelectItem value="on_leave">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">Cuti</Badge>
                      </div>
                    </SelectItem>
                    <SelectItem value="terminated">
                      <div className="flex items-center space-x-2">
                        <Badge variant="destructive" className="text-xs">Diberhentikan</Badge>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Schedule & Compensation Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 pb-2 border-b">
              <Clock className="h-4 w-4 text-primary" />
              <h3 className="font-semibold text-lg">Jadwal & Kompensasi</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="joinDate" className="text-sm font-medium">
                  Tanggal Bergabung <span className="text-red-500">*</span>
                </Label>
                <Input 
                  id="joinDate" 
                  type="date" 
                  {...register('joinDate')} 
                  className={errors.joinDate ? 'border-red-500' : ''}
                />
                {errors.joinDate && (
                  <p className="text-red-500 text-xs mt-1">{errors.joinDate.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="shift" className="text-sm font-medium">
                  Shift <span className="text-red-500">*</span>
                </Label>
                <Select value={selectedShift} onValueChange={(value: Shift) => {
                  setSelectedShift(value);
                  setValue('shift', value);
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih shift" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="morning">Pagi (08:00-14:00)</SelectItem>
                    <SelectItem value="afternoon">Siang (14:00-20:00)</SelectItem>
                    <SelectItem value="night">Malam (20:00-08:00)</SelectItem>
                    <SelectItem value="rotating">Rotating</SelectItem>
                    <SelectItem value="regular">Regular</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="salary" className="text-sm font-medium">
                  Gaji (Rp)
                </Label>
                <Input 
                  id="salary" 
                  type="number" 
                  placeholder="5000000"
                  min="0"
                  {...register('salary', { valueAsNumber: true })} 
                  className={errors.salary ? 'border-red-500' : ''}
                />
                {errors.salary && (
                  <p className="text-red-500 text-xs mt-1">{errors.salary.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 pb-2 border-b">
              <MapPin className="h-4 w-4 text-primary" />
              <h3 className="font-semibold text-lg">Informasi Tambahan</h3>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="address" className="text-sm font-medium">
                Alamat
              </Label>
              <Textarea 
                id="address" 
                placeholder="Alamat lengkap karyawan"
                rows={3}
                {...register('address')} 
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button variant="outline" type="button" onClick={onCancel} disabled={isLoading}>
              Batal
            </Button>
            <Button type="submit" disabled={isLoading} className="min-w-[120px]">
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                employee ? 'Perbarui' : 'Simpan'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

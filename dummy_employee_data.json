{"description": "Dummy Employee Data for <PERSON><PERSON> - Indonesian Employee Health Management System", "metadata": {"total_employees": 27, "roles": {"admin": 3, "manager": 4, "doctor": 5, "nurse": 5, "pharmacist": 3, "technician": 4, "receptionist": 3}, "departments": ["emergency", "surgery", "pediatrics", "cardiology", "orthopedics", "pharmacy", "laboratory", "radiology", "administration", "maintenance"], "default_password": "admin123", "password_hash": "$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S"}, "employees": [{"employeeId": "EMP001", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+62812-3456-7890", "role": "admin", "department": "administration", "position": "System Administrator", "joinDate": "2023-01-15", "status": "active", "shift": "regular", "salary": 15000000, "address": "<PERSON><PERSON><PERSON> No. 123, Jakarta Pusat", "emergencyContact": {"name": "<PERSON><PERSON>", "relationship": "<PERSON><PERSON>", "phone": "+62812-3456-7891", "email": "<EMAIL>"}, "certifications": ["System Administration", "Database Management", "Security Management"], "skills": ["PostgreSQL", "Linux", "Network Security", "Project Management"]}, {"employeeId": "EMP002", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+62813-4567-8901", "role": "admin", "department": "administration", "position": "IT Administrator", "joinDate": "2023-02-01", "status": "active", "shift": "regular", "salary": 14500000, "address": "Jl. <PERSON><PERSON> No. 456, Jakarta Pusat", "emergencyContact": {"name": "<PERSON><PERSON>", "relationship": "<PERSON><PERSON>", "phone": "+62813-4567-8902", "email": "<EMAIL>"}, "certifications": ["Network Administration", "Server Management", "Backup & Recovery"], "skills": ["Windows Server", "VMware", "Cisco", "Active Directory"]}, {"employeeId": "EMP003", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+62814-5678-9012", "role": "admin", "department": "administration", "position": "HR Administrator", "joinDate": "2023-03-10", "status": "active", "shift": "regular", "salary": 13500000, "address": "Jl. Gatot Subroto No. 789, Jakarta Selatan", "emergencyContact": {"name": "<PERSON><PERSON>", "relationship": "<PERSON><PERSON>", "phone": "+62814-5678-9013", "email": "<EMAIL>"}, "certifications": ["Human Resources", "Employee Relations", "Payroll Management"], "skills": ["HRIS", "Recruitment", "Training & Development", "Labor Law"]}, {"employeeId": "EMP004", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+62815-6789-0123", "role": "manager", "department": "emergency", "position": "Emergency Department Manager", "joinDate": "2022-06-15", "status": "active", "shift": "rotating", "salary": 18000000, "address": "<PERSON><PERSON><PERSON><PERSON> Said No. 321, Jakarta Selatan", "emergencyContact": {"name": "<PERSON><PERSON>", "relationship": "<PERSON><PERSON>", "phone": "+62815-6789-0124", "email": "<EMAIL>"}, "certifications": ["Emergency Medicine", "Crisis Management", "Team Leadership"], "skills": ["Emergency Protocols", "Triage", "Staff Management", "Quality Assurance"]}, {"employeeId": "EMP005", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+62816-7890-1234", "role": "manager", "department": "surgery", "position": "Surgery Department Manager", "joinDate": "2022-08-20", "status": "active", "shift": "morning", "salary": 17500000, "address": "Jl. <PERSON><PERSON> No. 654, Jakarta Selatan", "emergencyContact": {"name": "<PERSON><PERSON>", "relationship": "<PERSON><PERSON>", "phone": "+62816-7890-1235", "email": "<EMAIL>"}, "certifications": ["Surgical Management", "Operating Room Protocols", "Staff Coordination"], "skills": ["OR Management", "Surgical Instruments", "Sterilization", "Patient Safety"]}], "sample_login_credentials": [{"role": "admin", "email": "<EMAIL>", "password": "admin123", "description": "System Administrator with full access"}, {"role": "manager", "email": "<EMAIL>", "password": "admin123", "description": "Emergency Department Manager"}, {"role": "doctor", "email": "<EMAIL>", "password": "admin123", "description": "Emergency Medicine Specialist"}, {"role": "nurse", "email": "<EMAIL>", "password": "admin123", "description": "Emergency Nurse"}, {"role": "pharmacist", "email": "<EMAIL>", "password": "admin123", "description": "Clinical Pharmacist"}, {"role": "technician", "email": "<EMAIL>", "password": "admin123", "description": "Medical Laboratory Technician"}, {"role": "receptionist", "email": "<EMAIL>", "password": "admin123", "description": "Front Desk Receptionist"}], "usage_instructions": {"sql_import": "Run dummy_employee_data.sql in your PostgreSQL database", "api_import": "Use the employees array to create records via POST /api/v1/employees", "authentication": "All users can login with their email and password 'admin123'", "testing": "Use sample_login_credentials to test different role access levels"}}